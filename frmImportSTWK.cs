﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using DevExpress.XtraBars;
using System.IO;

namespace HIH.CRM.Import
{
    public partial class frmImportSTWK : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        #region 字段和属性

        private DataTable dtUnfinished = new DataTable(); // 未完成列表数据
        private DataTable dtImported = new DataTable();   // Excel导入列表数据
        private string currentFilePath = "";              // 当前Excel文件路径

        #endregion

        #region 构造函数和初始化

        public frmImportSTWK()
        {
            InitializeComponent();
        }


        #endregion

        #region 窗体事件

        private void frmImportSTWK_Load(object sender, EventArgs e)
        {
            try
            {
                // 加载未完成数据
                LoadUnfinishedData();

            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"窗体加载失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion


        #region 数据加载

        private void LoadUnfinishedData()
        {
            try
            {
                // 这里应该从数据库查询未完成的数据
                // 示例SQL: SELECT * FROM 进口核注清单明细表 WHERE 剩余申报数量 > 0
                string sql = @"SELECT
                    a.ID,
                    b.核注清单编号,
                    a.商品序号,
                    a.备案序号,
                    a.商品料号,
                    a.商品编码,
                    a.商品名称,
                    a.单价,
                    a.总价,
                    a.币制,
                    a.申报数量,
                    a.剩余申报数量,
                    a.法定数量,
                    a.剩余法定数量,
                    '未完成' as 状态,
                    b.操作时间
                FROM 进口核注清单明细表 a
                INNER JOIN 进口核注清单表 b ON a.上级ID = b.ID
                WHERE a.剩余申报数量 > 0
                ORDER BY b.操作时间 DESC";

                // 这里需要使用您的数据访问层来执行查询
                // DataTable result = YourDataAccess.ExecuteQuery(sql);
                // dtUnfinished = result;
                // gridControlUnfinished.DataSource = dtUnfinished;

                // 临时添加一些示例数据用于演示
                AddSampleUnfinishedData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载未完成数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddSampleUnfinishedData()
        {
            // 添加示例数据
            for (int i = 1; i <= 5; i++)
            {
                DataRow row = dtUnfinished.NewRow();
                row["ID"] = i;
                row["核注清单编号"] = $"HZQD{DateTime.Now:yyyyMMdd}{i:000}";
                row["商品序号"] = $"SP{i:000}";
                row["备案序号"] = $"BA{i:000}";
                row["商品料号"] = $"LH{i:000}";
                row["商品编码"] = $"CODE{i:000}";
                row["商品名称"] = $"商品名称{i}";
                row["单价"] = 100.00m + i;
                row["总价"] = (100.00m + i) * (10 + i);
                row["币制"] = "USD";
                row["申报数量"] = 10 + i;
                row["剩余申报数量"] = 5 + i;
                row["法定数量"] = 10 + i;
                row["剩余法定数量"] = 5 + i;
                row["状态"] = "未完成";
                row["操作时间"] = DateTime.Now.AddDays(-i);
                dtUnfinished.Rows.Add(row);
            }
        }

        #endregion

        #region 工具栏按钮事件

        private void btnImportExcel_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Excel文件|*.xlsx;*.xls";
                openFileDialog.Title = "选择要导入的Excel文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    currentFilePath = openFileDialog.FileName;
                    ImportExcelFile(currentFilePath);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"导入Excel失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                LoadUnfinishedData();
                XtraMessageBox.Show("数据刷新完成", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"刷新数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSave_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {

                XtraMessageBox.Show("数据保存完成", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"保存数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void btnReturn_ItemClick(object sender, ItemClickEventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Excel导入处理

        private void ImportExcelFile(string filePath)
        {
            try
            {
                // 清空之前的导入数据
                dtImported.Clear();

                XtraMessageBox.Show($"成功导入 {dtImported.Rows.Count} 条数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败：{ex.Message}");
            }
        }

        #endregion

        #region 数据操作


        #endregion

        #region 网格样式事件

        private void gridViewUnfinished_RowStyle(object sender, RowStyleEventArgs e)
        {
            try
            {
                GridView view = sender as GridView;
                if (view == null) return;

                // 根据剩余数量设置行颜色
                object remainingQty = view.GetRowCellValue(e.RowHandle, "剩余申报数量");
                if (remainingQty != null && remainingQty != DBNull.Value)
                {
                    decimal remaining = Convert.ToDecimal(remainingQty);
                    if (remaining <= 0)
                    {
                        e.Appearance.BackColor = Color.LightGray;
                        e.Appearance.ForeColor = Color.DarkGray;
                    }
                    else if (remaining <= 5)
                    {
                        e.Appearance.BackColor = Color.LightYellow;
                    }
                }
            }
            catch (Exception ex)
            {
                // 忽略样式设置错误
            }
        }

        private void gridViewImported_RowStyle(object sender, RowStyleEventArgs e)
        {
            try
            {
                GridView view = sender as GridView;
                if (view == null) return;

                // 根据导入状态设置行颜色
                object status = view.GetRowCellValue(e.RowHandle, "导入状态");
                if (status != null)
                {
                    string statusStr = status.ToString();
                    switch (statusStr)
                    {
                        case "已处理":
                        case "已保存":
                            e.Appearance.BackColor = Color.LightGreen;
                            break;
                        case "待处理":
                            e.Appearance.BackColor = Color.LightYellow;
                            break;
                        case "错误":
                            e.Appearance.BackColor = Color.LightCoral;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                // 忽略样式设置错误
            }
        }

        #endregion
    }
}
