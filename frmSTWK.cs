using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Collections;


namespace HIH.CRM.Import
{
    public partial class frmSTWK : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        public frmSTWK()
        {
            InitializeComponent();
        }

        private void gdv_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gdv.CalcHitInfo(new Point(e.X, e.Y));
                if (e.Button == MouseButtons.Left && e.Clicks == 2)//判断是否左键双击
                {
                    //判断光标是否在行范围内
                    if (hInfo.InRow)
                    {

                    }
                }
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError(ex.Message);
            }
        }

        private void btnImport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                ArrayList al = new ArrayList();

                ICF.ISD.SubFormShowModal("HIH.CRM.Import.dll",
                                         "HIH.CRM.Import.frmImportSTWK",
                                         "导入",
                                         al
                                         );
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
    }
}
