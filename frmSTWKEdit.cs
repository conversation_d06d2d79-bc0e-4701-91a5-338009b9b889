﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.CRM.Import
{
    public partial class frmSTWKEdit : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        public frmSTWKEdit()
        {
            InitializeComponent();
        }

        private void CHKDDJS_CheckedChanged(object sender, EventArgs e)
        {
            ddJSSJ.ReadOnly = !chkDDJS.Checked;
            if (chkDDJS.Checked)
            {
                ddJSSJ.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblDDJSCZR.Text = ICF.ISO.UNAME;
                lblDDJSSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        private void chkFSDD_CheckedChanged(object sender, EventArgs e)
        {
            ddFSDD.ReadOnly = !chkDDJS.Checked;
            if (chkDDJS.Checked)
            {
                ddFSDD.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblFSDDCZR.Text = ICF.ISO.UNAME;
                lblFSDDSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddFSDD.EditValue = DBNull.Value;
                lblFSDDCZR.Text = "";
                lblFSDDSJ.Text = "";
            }
        }

        private void chkFHRHF_CheckedChanged(object sender, EventArgs e)
        {
            ddFHRHF.ReadOnly = !chkDDJS.Checked;
            if (chkFHRHF.Checked)
            {
                ddFHRHF.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblFHHFCZR.Text = ICF.ISO.UNAME;
                lblFHHFSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddFHRHF.EditValue = DBNull.Value;
                lblFHHFCZR.Text = "";
                lblFHHFSJ.Text = "";
            }
        }

        private void chkSJJF_CheckedChanged(object sender, EventArgs e)
        {
            ddSJJF.ReadOnly = !chkDDJS.Checked;
            if (chkSJJF.Checked)
            {
                ddSJJF.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblSJJFCZR.Text = ICF.ISO.UNAME;
                lblSHHFSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddSJJF.EditValue = DBNull.Value;
                lblSJJFCZR.Text = "";
                lblSHHFSJ.Text = "";
            }
        }
    }
}
